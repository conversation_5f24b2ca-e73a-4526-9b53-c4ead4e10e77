const mongoose = require("mongoose"),
  port = process.env.PORT || 3000,
  { server } = require("./app.socket.io");
require("dotenv").config();
mongoose.set("useNewUrlParser", true);
mongoose.set("useFindAndModify", false);
mongoose.set("useCreateIndex", true);
mongoose.set("useUnifiedTopology", true);
console.log(process.env.mongoDBURI);
mongoose.connect(
  process.env.mongoDBURI,
  { dbName: "chatNBot" },
  handleConnection
);
function handleConnection(err) {
  if (err) throw err;
  console.log("Connected to Mongo database");
}
module.exports = server.listen(port, () =>
  console.log(`Example app listening on port ${port}!`)
);
