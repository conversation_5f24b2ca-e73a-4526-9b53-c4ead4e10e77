var responseHandler = require("./responseHandler"),
  dataAccess = require("./DB/dataAccess"),
  { replyBack } = require("./requestHandler"),
  messages = require("./responseMessages");

module.exports.helloWorld = (req, res) => res.send("Hello World!");
module.exports.verify = (req, res) => res.send("service is up and running!");
module.exports.getWebhook = (req, res) =>
  req.query["hub.verify_token"] == process.env.VERIFY_TOKEN
    ? res.send(req.query["hub.challenge"])
    : res.send("Hello There");

/*******
 * TODO:
 * 1- add the ability to message between several pages
 */
// Accepts POST requests at /webhook endpoint
module.exports.postWebhook = (req, res) => {
  let body = req.body;
  //console.log("***********postWebhook********");
  //console.log(body);
  // //console.log()
  if (body.object === "page") {
    body.entry.forEach(function (entry) {
      // Gets the body of the webhook event
      let webhook_event = entry.messaging[0];
      replyBack(webhook_event);
      res.status(200).send("EVENT_RECEIVED");
    });
    // Return a '200 OK' response to all events
  } else {
    // Return a '404 Not Found' if event is not from a page subscription
    res.sendStatus(404);
  }
};

module.exports.uploadImage = (req, res) => {
  let body = req.body;
  //console.log("********** uploadImage *********");
  // //console.log()
  //console.log(body);
};
