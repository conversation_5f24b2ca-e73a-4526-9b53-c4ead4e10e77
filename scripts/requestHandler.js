var responseHandler = require("./responseHandler"),
  dataAccess = require("./DB/dataAccess"),
  messages = require("./responseMessages");
module.exports.replyBack = (body) => {
  //console.log("******************* body *********************");
  //console.log(body);
  let sender = getSender(body);
  //handleCommands
  if (body.postback) {
    handlePostback(sender, body.postback);
  } else if (body.message) {
    handleMessaging(sender, body);
  }
};
function getSender(body) {
  return {
    userId: body.sender.id,
    channelID: body.recipient.id,
  };
}
function handleMessaging(sender, body) {
  dataAccess
    .getReceiver(sender)
    .then((receiver) => {
      handleReceiverId(receiver, sender, body);
    })
    .catch((err) => {
      //console.log(err);
    });
}

function handleReceiverId(receiver, sender, body) {
  //same user , send waiting message
  if (receiver.userId == sender.userId) {
    responseHandler.handleMessage(receiver, {
      text: messages.messages[
        Math.floor(Math.random() * messages.messages.length)
      ],
    });
  } else {
    // console.debug(`chatting with others ${receiver.justMatched}`);
    if (receiver.justMatched) {
      textBothParticipants(receiver, sender, messages.nowConnected);
    }
    responseHandler.handleMessage(receiver, body.message);
  }
}

function textBothParticipants(receiver, sender, text) {
  textParticipant(receiver, text);
  textParticipant(sender, text);
}
function textParticipant(user, text) {
  if (user) responseHandler.handleMessage(user, { text: text });
}
/**
 * This is for handling the presaved menu items (reconnect , disconnect , help , getstarted)
 * @param {Object} sender
 * @param {Object} received_postback
 */

function handlePostback(sender, received_postback) {
  let payload = received_postback.payload;
  //console.log("********** payload **********", payload);
  if (payload === "RECONNECT") {
    //remove from chatting add to waiting
    handleReconnect(sender);
  } else if (payload === "DISCONNECT") {
    handleDisconnect(sender);
  } else textParticipant(sender, messages.help); //help and get_started
  // responseHandler.handlePostback(sender_psid, received_postback);
}
function handleDisconnect(sender) {
  dataAccess.removeFromChatting(sender).then((data) => {
    let user = data;
    if (data.participants) {
      //if chatting object not user
      user = messageBothAndGetSender(data, sender);
    }
    removeUserFromWaiting(user);
  });
}

function handleReconnect(sender) {
  dataAccess.removeFromChatting(sender).then((data) => {
    let user = data;
    if (data.participants) {
      user = messageBothAndGetSender(data, sender);
    }
    addUserToWaiting(user);
  });
}

function messageBothAndGetSender(data, sender) {
  textBothParticipants(
    data.participants[0],
    data.participants[1],
    messages.partnerLeft
  );
  return data.participants.filter(
    (participant) => participant.userId == sender.userId
  )[0];
}

function removeUserFromWaiting(sender) {
  dataAccess.removeFromWaiting(sender).then((data) => {
    textParticipant(sender, messages.disconnect);
  });
}

function addUserToWaiting(sender) {
  dataAccess.addUserToWaiting(sender).then((data) => {
    if (data.justMatched)
      handleReceiverId(data, sender, { message: { text: "Hi" } });
    else textParticipant(sender, messages.reconnected);
  });
}
