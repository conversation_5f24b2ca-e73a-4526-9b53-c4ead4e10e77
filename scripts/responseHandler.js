const request = require("request");
const socket = require("../app.socket.io");

module.exports.handleMessage = (user, received_message) => {
  let response;

  // Checks if the message contains text
  if (received_message.text) {
    // Create the payload for a basic text message, which
    // will be added to the body of our request to the Send API
    response = {
      text: received_message.text,
    };
  } else if (received_message.attachments) {
    let attachment_url = received_message.attachments[0].payload.url;
    let type = received_message.attachments[0].type;
    response = {
      attachment: {
        type: type,
        payload: {
          url: attachment_url,
        },
      },
    };
  }

  // Send the response message
  //console.log("user.channelID ", user.channelID);
  if (user.channelID === "app") {
    socket.sendMessageToUser(user.userId, response);
  } else {
    callSendAPI(user.userId, response, user.channelID);
  }
};

function callSendAPI(user_psid, response, channelID) {
  // Construct the message body
  let request_body = {
    recipient: {
      id: user_psid,
    },
    message: response,
  };

  // Send the HTTP request to the Messenger Platform
  request(
    {
      uri: "https://graph.facebook.com/v11.0/me/messages",
      qs: { access_token: process.env[channelID] },
      method: "POST",
      json: request_body,
    },
    (err) => {
      if (!err) {
        // console.log("message sent!");
      } else {
        console.error("Unable to send message:" + err);
      }
    }
  );
}
