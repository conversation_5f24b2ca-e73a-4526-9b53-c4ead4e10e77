var mongoose = require("mongoose");
var Schema = mongoose.Schema;

var schema = new Schema(
  {
    participants: [{ type: Schema.Types.ObjectId, ref: "User" }],
  },
  { timestamps: {} }
);
schema.post("findOne", function (doc, next) {
  if (doc) {
    let users = doc.participants.filter((user) => {
      var date = new Date(user.updatedAt);
      date.setDate(date.getDate() + 1);
      return date - new Date() < 0;
    });
    if (users.length) {
      doc.remove();
      next(new Error("OtherUserInactive"));
    } else next();
  } else next();
});
module.exports = mongoose.model("Chatting", schema);
