const User = require("./user");
exports.create = function (sender) {
  var user = new User();
  user.userId = sender.userId;
  user.channelID = sender.channelID;
  return user.save();
};
exports.find = (sender) => {
  return User.findOneAndUpdate(
    { userId: sender.userId, channelID: sender.channelID },
    { userId: sender.userId, channelID: sender.channelID },
    { upsert: true, new: true }
  ).exec();
};
