const Waiting = require("./waiting");

exports.fetch = (user) => {
  let yesterday = new Date();
  // subtracting 1 day
  yesterday.setDate(yesterday.getDate() - 1);
  return Waiting.deleteMany({ updatedAt: { $lte: yesterday } })
    .exec()
    .then((data) => {
      return Waiting.findOneAndDelete({ user: { $ne: user } })
        .populate("user")
        .exec();
    });
};
exports.add = (user) => {
  return Waiting.findOneAndUpdate(
    { user: user },
    { user: user },
    { upsert: true, new: true }
  )
    .populate("user")
    .exec();
};
exports.remove = (sender) => {
  return Waiting.deleteOne({ user: sender }).exec();
};
