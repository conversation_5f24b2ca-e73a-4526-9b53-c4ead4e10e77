const UserController = require("./schemes/user/user.ctrl"),
  ChattingController = require("./schemes/chatting/chatting.ctrl"),
  WaitingController = require("./schemes/waiting/waiting.ctrl");
exports.getReceiver = (req_sender) => {
  return UserController.find(req_sender).then((sender) => {
    return ChattingController.find(sender)
      .then((chatting) => {
        // console.debug(
        //   `find if user in chatting sender: ${sender} chatting : ${chatting}`
        // );
        if (chatting) {
          //get the recieptID
          let [reciever] = chatting.participants.filter((participant) => {
            if (participant.id != sender.id) return participant;
          });

          return new Promise((resolve) => {
            //console.log(`reciever ${reciever}`);
            resolve(reciever);
          });
        } else {
          return exports.addUserToWaiting(sender);
        }
      })
      .catch((err) => {
        if (err.message == "OtherUserInactive")
          return exports.addUserToWaiting(sender);
      });
  });
};
exports.removeFromChatting = (senderId) => {
  return UserController.find(senderId).then((sender) => {
    return ChattingController.remove(sender).then((data) =>
      data ? data : sender
    );
  });
};

exports.removeFromWaiting = (sender) => {
  return WaitingController.remove(sender);
};

exports.addUserToWaiting = (sender) => {
  return WaitingController.fetch(sender).then((waiting) => {
    if (waiting) {
      // console.debug("adding user to waiting ");
      ChattingController.create(sender, waiting.user);
      return new Promise((resolve) => {
        resolve({ ...waiting.user.toObject(), justMatched: true });
      });
    } else {
      return WaitingController.add(sender).then((userAdded) => {
        return userAdded.user;
      });
    }
  });
};
