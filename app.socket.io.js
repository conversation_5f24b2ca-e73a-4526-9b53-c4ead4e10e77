const { uploadImg } = require("./google.config");

const app = require("./app.config"),
  server = require("http").createServer(app),
  { replyBack } = require("./scripts/requestHandler"),
  io = require("socket.io")();
io.attach(server);

let images = [];
io.on("connection", (socket) => {
  // //console.log("connection");
  socket.on("chat message", (msg) => {
    //console.log("**********chat message************");
    //console.log("chat message", socket.id, msg);
    replyBack(createMsg(msg, socket.id));
    io.to(socket.id).emit("ack", { id: msg.id });
  });
  socket.on("image", (msg, ack) => {
    //process data with  user
    // console.log("image  ", msg.index);
    if (!images) images = [];
    if (!images[socket.id]) images[socket.id] = [];
    if (!images[socket.id][msg.id]) images[socket.id][msg.id] = msg.data;
    else images[socket.id][msg.id] += msg.data;
    ack(msg.index);
  });
  socket.on("imageSent", (msg, ack) => {
    // console.log("imageSent");
    if (images && images[socket.id] && images[socket.id][msg.id]) {
      let base64 = images[socket.id][msg.id];
      let resp = uploadImg("FriendlyChat", base64, msg.id);
      delete images[socket.id][msg.id];
      if (images[socket.id].length === 0) delete images[socket.id];
      resp
        .then((imgUrl) => {
          replyBack(createAttachmentMsg(socket.id, imgUrl));
          io.to(socket.id).emit("ack", { id: msg.id });
        })
        .catch(console.error);
      console.log(ack);
      ack("image recieved");
    }
  });
  socket.on("disconnect", (reason) => {
    // console.log("Disconnect", reason);
    // console.log("Disconnect socket.id", socket.id);
    delete images[socket.id];
    replyBack(createPostBackMsg(socket.id, "Disconnect"));
  });
  socket.on("logout", (reason) => {
    //console.log("logout", reason);
    //console.log("logout", socket.id);
    delete images[socket.id];
    replyBack(createPostBackMsg(socket.id, "Disconnect"));
  });
  socket.on("reconnect", (reason) => {
    //console.log("Reconnect", reason);
    delete images[socket.id];
    replyBack(createPostBackMsg(socket.id, "Reconnect"));
  });
  socket.on("error", (reason) => {
    //console.log("error", reason);
    delete images[socket.id];
    replyBack(createPostBackMsg(socket.id, "Reconnect"));
  });

  socket.on("connect_failed", function () {
    //console.log("Sorry, there seems to be an issue with the connection!");
  });
});
function createMsg(msg, senderId) {
  return {
    ...createSender(senderId),
    ...createMessage(msg),
  };
}
function createAttachmentMsg(senderId, imageUrl) {
  return {
    ...createSender(senderId),
    ...createAttachment(imageUrl),
  };
}
function createPostBackMsg(senderId, actionButton) {
  return {
    ...createSender(senderId),
    ...createPostback(actionButton),
  };
}
function createMessage(msg) {
  return msg && msg.text && { message: { text: msg.text } };
}
function createPostback(type) {
  if (type) return { postback: { title: type, payload: type.toUpperCase() } };
}
function createAttachment(imageUrl) {
  if (imageUrl)
    return {
      message: {
        attachments: [
          {
            type: "image",
            payload: {
              url: imageUrl,
            },
          },
        ],
      },
    };
}
function createSender(senderId) {
  return {
    sender: {
      id: senderId,
    },
    recipient: {
      id: "app",
    },
  };
}
function sendMessageToUser(userId, msg) {
  io.to(userId).emit("serverMessage", msg);
}
module.exports.server = server;
module.exports.sendMessageToUser = sendMessageToUser;
