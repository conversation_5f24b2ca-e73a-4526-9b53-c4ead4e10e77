const responseHandler = require("../scripts/responseHandler");
const request = require("request");
const socket = require("../app.socket.io");

// Mock dependencies
jest.mock("request");
jest.mock("../app.socket.io");

describe("Response Handler", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("handleMessage", () => {
    it("should handle text messages for app channel", () => {
      const user = { userId: "user123", channelID: "app" };
      const message = { text: "Hello world" };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });

    it("should handle text messages for external channels", () => {
      const user = { userId: "user123", channelID: "facebook_channel" };
      const message = { text: "Hello world" };

      process.env.facebook_channel = "test_access_token";
      request.mockImplementation((options, callback) => {
        callback(null);
      });

      responseHandler.handleMessage(user, message);

      expect(request).toHaveBeenCalledWith({
        uri: "https://graph.facebook.com/v11.0/me/messages",
        qs: { access_token: "test_access_token" },
        method: "POST",
        json: {
          recipient: { id: "user123" },
          message: { text: "Hello world" }
        }
      }, expect.any(Function));
    });

    it("should handle attachment messages for app channel", () => {
      const user = { userId: "user123", channelID: "app" };
      const message = {
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }
      });
    });

    it("should handle attachment messages for external channels", () => {
      const user = { userId: "user123", channelID: "facebook_channel" };
      const message = {
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      process.env.facebook_channel = "test_access_token";
      request.mockImplementation((options, callback) => {
        callback(null);
      });

      responseHandler.handleMessage(user, message);

      expect(request).toHaveBeenCalledWith({
        uri: "https://graph.facebook.com/v11.0/me/messages",
        qs: { access_token: "test_access_token" },
        method: "POST",
        json: {
          recipient: { id: "user123" },
          message: {
            attachment: {
              type: "image",
              payload: { url: "https://example.com/image.png" }
            }
          }
        }
      }, expect.any(Function));
    });

    it("should handle API errors gracefully", () => {
      const user = { userId: "user123", channelID: "facebook_channel" };
      const message = { text: "Hello world" };

      process.env.facebook_channel = "test_access_token";
      const mockError = new Error("API Error");
      request.mockImplementation((options, callback) => {
        callback(mockError);
      });

      // Should not throw error
      expect(() => {
        responseHandler.handleMessage(user, message);
      }).not.toThrow();

      expect(request).toHaveBeenCalled();
    });

    it("should handle multiple attachment types", () => {
      const user = { userId: "user123", channelID: "app" };
      const videoMessage = {
        attachments: [{
          type: "video",
          payload: { url: "https://example.com/video.mp4" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, videoMessage);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        attachment: {
          type: "video",
          payload: { url: "https://example.com/video.mp4" }
        }
      });
    });

    it("should handle empty or invalid messages", () => {
      const user = { userId: "user123", channelID: "app" };
      
      socket.sendMessageToUser = jest.fn();

      // Test with empty message
      responseHandler.handleMessage(user, {});
      expect(socket.sendMessageToUser).not.toHaveBeenCalled();

      // Test with null message
      responseHandler.handleMessage(user, null);
      expect(socket.sendMessageToUser).not.toHaveBeenCalled();

      // Test with undefined message
      responseHandler.handleMessage(user, undefined);
      expect(socket.sendMessageToUser).not.toHaveBeenCalled();
    });

    it("should prioritize text over attachments", () => {
      const user = { userId: "user123", channelID: "app" };
      const message = {
        text: "Hello world",
        attachments: [{
          type: "image",
          payload: { url: "https://example.com/image.png" }
        }]
      };

      socket.sendMessageToUser = jest.fn();

      responseHandler.handleMessage(user, message);

      expect(socket.sendMessageToUser).toHaveBeenCalledWith("user123", {
        text: "Hello world"
      });
    });
  });
});
