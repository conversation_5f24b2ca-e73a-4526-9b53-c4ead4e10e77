// Basic tests that don't require database connections

// Set up test environment before importing modules
process.env.NODE_ENV = "test";
process.env.VERIFY_TOKEN = "test_verify_token";

// Mock all external dependencies before importing
const mockSchema = jest.fn().mockImplementation(() => ({
  post: jest.fn(),
  pre: jest.fn()
}));
mockSchema.Types = {
  ObjectId: jest.fn()
};

jest.mock("mongoose", () => ({
  connect: jest.fn().mockResolvedValue({}),
  connection: {
    readyState: 1,
    close: jest.fn().mockResolvedValue({}),
    collections: {}
  },
  set: jest.fn(),
  model: jest.fn(),
  Schema: mockSchema
}));

jest.mock("../scripts/DB/dataAccess");
jest.mock("../scripts/responseHandler");
jest.mock("../google.config");
jest.mock("../scripts/requestHandler");

const messages = require("../scripts/responseMessages");

// Test helper functions
const createTestUser = (userId = "test_user_1", channelID = "test_channel") => ({
  userId,
  channelID,
});

const createTestMessage = (text = "test message", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: { text },
});

const createTestAttachment = (url = "https://example.com/image.png", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: {
    attachments: [
      {
        type: "image",
        payload: { url },
      },
    ],
  },
});

const createTestPostback = (payload = "RECONNECT", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  postback: {
    title: payload.toLowerCase(),
    payload,
  },
});

describe("Basic Functionality Tests", () => {
  describe("Response Messages", () => {
    it("should have waiting messages array", () => {
      expect(messages.messages).toBeDefined();
      expect(Array.isArray(messages.messages)).toBe(true);
      expect(messages.messages.length).toBeGreaterThan(0);
      
      messages.messages.forEach(message => {
        expect(typeof message).toBe("string");
        expect(message.length).toBeGreaterThan(0);
      });
    });

    it("should have all required message types", () => {
      expect(messages.nowConnected).toBeDefined();
      expect(typeof messages.nowConnected).toBe("string");
      
      expect(messages.partnerLeft).toBeDefined();
      expect(typeof messages.partnerLeft).toBe("string");
      
      expect(messages.disconnect).toBeDefined();
      expect(typeof messages.disconnect).toBe("string");
      
      expect(messages.reconnected).toBeDefined();
      expect(typeof messages.reconnected).toBe("string");
      
      expect(messages.help).toBeDefined();
      expect(typeof messages.help).toBe("string");
    });

    it("should have meaningful message content", () => {
      expect(messages.nowConnected).toContain("match");
      expect(messages.partnerLeft).toContain("left");
      expect(messages.disconnect).toContain("leaving");
      expect(messages.reconnected).toContain("partner");
      expect(messages.help).toContain("message");
    });
  });

  describe("Request Receiver Functions", () => {
    const { helloWorld, verify, getWebhook } = require("../scripts/requestReciever");

    it("should have helloWorld function", () => {
      expect(typeof helloWorld).toBe("function");
      
      const mockReq = {};
      const mockRes = { send: jest.fn() };
      
      helloWorld(mockReq, mockRes);
      expect(mockRes.send).toHaveBeenCalledWith("Hello World!");
    });

    it("should have verify function", () => {
      expect(typeof verify).toBe("function");
      
      const mockReq = {};
      const mockRes = { send: jest.fn() };
      
      verify(mockReq, mockRes);
      expect(mockRes.send).toHaveBeenCalledWith("service is up and running!");
    });

    it("should handle webhook verification", () => {
      process.env.VERIFY_TOKEN = "test_token";
      
      const mockReq = {
        query: {
          "hub.verify_token": "test_token",
          "hub.challenge": "challenge_code"
        }
      };
      const mockRes = { send: jest.fn() };
      
      getWebhook(mockReq, mockRes);
      expect(mockRes.send).toHaveBeenCalledWith("challenge_code");
    });

    it("should reject invalid webhook verification", () => {
      process.env.VERIFY_TOKEN = "test_token";
      
      const mockReq = {
        query: {
          "hub.verify_token": "wrong_token",
          "hub.challenge": "challenge_code"
        }
      };
      const mockRes = { send: jest.fn() };
      
      getWebhook(mockReq, mockRes);
      expect(mockRes.send).toHaveBeenCalledWith("Hello There");
    });
  });

  describe("Utility Functions", () => {
    // Test utility functions that don't require external dependencies
    
    it("should create message objects correctly", () => {
      const createMessage = (msg) => {
        return msg && msg.text && msg.text.length > 0 ? { message: { text: msg.text } } : undefined;
      };

      expect(createMessage({ text: "Hello" })).toEqual({
        message: { text: "Hello" }
      });

      expect(createMessage({ text: "" })).toBeUndefined();
      expect(createMessage({})).toBeUndefined();
      expect(createMessage(null)).toBeUndefined();
    });

    it("should create postback objects correctly", () => {
      const createPostback = (type) => {
        if (type) return { postback: { title: type, payload: type.toUpperCase() } };
      };

      expect(createPostback("reconnect")).toEqual({
        postback: { title: "reconnect", payload: "RECONNECT" }
      });
      
      expect(createPostback("")).toBeUndefined();
      expect(createPostback(null)).toBeUndefined();
    });

    it("should create sender objects correctly", () => {
      const createSender = (senderId) => {
        return {
          sender: { id: senderId },
          recipient: { id: "app" }
        };
      };

      expect(createSender("user123")).toEqual({
        sender: { id: "user123" },
        recipient: { id: "app" }
      });
    });

    it("should extract sender information correctly", () => {
      const getSender = (body) => {
        return {
          userId: body.sender.id,
          channelID: body.recipient.id,
        };
      };

      const body = {
        sender: { id: "user123" },
        recipient: { id: "channel456" }
      };
      
      expect(getSender(body)).toEqual({
        userId: "user123",
        channelID: "channel456"
      });
    });
  });

  describe("Environment Configuration", () => {
    it("should have test environment variables set", () => {
      expect(process.env.NODE_ENV).toBe("test");
      expect(process.env.VERIFY_TOKEN).toBeDefined();
    });

    it("should handle missing environment variables gracefully", () => {
      const originalToken = process.env.VERIFY_TOKEN;
      delete process.env.VERIFY_TOKEN;
      
      const { getWebhook } = require("../scripts/requestReciever");
      const mockReq = {
        query: {
          "hub.verify_token": "any_token",
          "hub.challenge": "challenge_code"
        }
      };
      const mockRes = { send: jest.fn() };
      
      getWebhook(mockReq, mockRes);
      expect(mockRes.send).toHaveBeenCalledWith("Hello There");
      
      // Restore original value
      process.env.VERIFY_TOKEN = originalToken;
    });
  });

  describe("Test Helpers", () => {
    it("should have test helper functions", () => {
      expect(typeof createTestUser).toBe("function");
      expect(typeof createTestMessage).toBe("function");
      expect(typeof createTestAttachment).toBe("function");
      expect(typeof createTestPostback).toBe("function");
    });

    it("should create test data correctly", () => {
      const user = createTestUser("user123", "channel456");
      expect(user).toEqual({
        userId: "user123",
        channelID: "channel456"
      });

      const message = createTestMessage("Hello", "user123");
      expect(message.sender.id).toBe("user123");
      expect(message.message.text).toBe("Hello");

      const attachment = createTestAttachment("https://example.com/image.png", "user123");
      expect(attachment.sender.id).toBe("user123");
      expect(attachment.message.attachments[0].payload.url).toBe("https://example.com/image.png");

      const postback = createTestPostback("RECONNECT", "user123");
      expect(postback.sender.id).toBe("user123");
      expect(postback.postback.payload).toBe("RECONNECT");
    });
  });
});
