const mongoose = require("mongoose");

// Enhanced MongoDB Memory Server setup with better error handling
let MongoMemoryServer;
let useInMemoryDB = false;

try {
  const { MongoMemoryServer: MemoryServer } = require("mongodb-memory-server");
  MongoMemoryServer = MemoryServer;
  useInMemoryDB = true;
} catch (error) {
  console.warn("MongoDB Memory Server not available, using mocked database");
  // Create a mock MongoMemoryServer for environments where it's not available
  MongoMemoryServer = class MockMongoMemoryServer {
    static async create() {
      return new MockMongoMemoryServer();
    }

    getUri() {
      return "mongodb://localhost:27017/test-mock";
    }

    async stop() {
      return Promise.resolve();
    }
  };
}

let mongoServer;

// Setup test database
beforeAll(async () => {
  try {
    // Set test environment variables first
    process.env.NODE_ENV = "test";
    process.env.VERIFY_TOKEN = "test_verify_token";
    process.env.TEST_CHANNEL_ID = "test_access_token";

    if (useInMemoryDB) {
      try {
        // Try to use real in-memory MongoDB
        mongoServer = await MongoMemoryServer.create({
          instance: {
            dbName: "test-db"
          }
        });
        const mongoUri = mongoServer.getUri();
        process.env.mongoDBURI = mongoUri;

        // Connect to the in-memory database with timeout
        await mongoose.connect(mongoUri, {
          useNewUrlParser: true,
          useUnifiedTopology: true,
          useCreateIndex: true,
          useFindAndModify: false,
          serverSelectionTimeoutMS: 5000, // 5 second timeout
          connectTimeoutMS: 5000,
        });

        console.log("✅ Connected to MongoDB Memory Server");
      } catch (mongoError) {
        console.warn("⚠️ MongoDB Memory Server failed:", mongoError.message);
        useInMemoryDB = false;
      }
    }

    if (!useInMemoryDB) {
      // Use mock database setup
      console.log("🔧 Using mocked database for tests");
      process.env.mongoDBURI = "mongodb://localhost:27017/test-mock";

      // Don't actually connect to MongoDB, just set up mocks
      mongoose.connect = jest.fn().mockResolvedValue({});
      mongoose.connection.readyState = 1;
    }
  } catch (error) {
    console.warn("Test setup warning:", error.message);
    // Ensure we have a fallback
    process.env.mongoDBURI = "mongodb://localhost:27017/test-fallback";
  }
}, 30000); // 30 second timeout for setup

// Clean up database between tests
beforeEach(async () => {
  if (useInMemoryDB && mongoose.connection.readyState === 1) {
    try {
      const collections = mongoose.connection.collections;
      for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
      }
    } catch (error) {
      // Ignore cleanup errors in test environment
      console.warn("Database cleanup warning:", error.message);
    }
  }
});

// Cleanup after all tests
afterAll(async () => {
  try {
    if (useInMemoryDB && mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
    if (mongoServer && typeof mongoServer.stop === 'function') {
      await mongoServer.stop();
    }
  } catch (error) {
    console.warn("Test cleanup warning:", error.message);
  }
}, 10000); // 10 second timeout for cleanup

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Helper functions for tests
global.createTestUser = (userId = "test_user_1", channelID = "test_channel") => ({
  userId,
  channelID,
});

global.createTestMessage = (text = "test message", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: { text },
});

global.createTestAttachment = (url = "https://example.com/image.png", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: {
    attachments: [
      {
        type: "image",
        payload: { url },
      },
    ],
  },
});

global.createTestPostback = (payload = "RECONNECT", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  postback: {
    title: payload.toLowerCase(),
    payload,
  },
});
