const mongoose = require("mongoose");

// Try to import MongoMemoryServer, fallback to mock if not available
let MongoMemoryServer;
try {
  MongoMemoryServer = require("mongodb-memory-server").MongoMemoryServer;
} catch (error) {
  // Fallback for environments where mongodb-memory-server is not available
  MongoMemoryServer = class MockMongoMemoryServer {
    static async create() {
      return new MockMongoMemoryServer();
    }

    getUri() {
      return "mongodb://localhost:27017/test";
    }

    async stop() {
      // Mock stop method
    }
  };
}

let mongoServer;

// Setup test database
beforeAll(async () => {
  try {
    // Set test environment variables first
    process.env.NODE_ENV = "test";
    process.env.VERIFY_TOKEN = "test_verify_token";
    process.env.TEST_CHANNEL_ID = "test_access_token";

    // Try to use in-memory MongoDB for testing
    try {
      mongoServer = await MongoMemoryServer.create();
      const mongoUri = mongoServer.getUri();
      process.env.mongoDBURI = mongoUri;

      // Connect to the in-memory database
      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        useCreateIndex: true,
        useFindAndModify: false,
      });
    } catch (mongoError) {
      // Fallback to mock database if MongoDB Memory Server fails
      console.warn("MongoDB Memory Server failed, using mock database");
      process.env.mongoDBURI = "mongodb://localhost:27017/test-fallback";

      // Mock mongoose for tests that don't need real database
      jest.mock("mongoose", () => ({
        connect: jest.fn().mockResolvedValue({}),
        connection: {
          readyState: 1,
          close: jest.fn().mockResolvedValue({}),
          collections: {}
        },
        set: jest.fn(),
        model: jest.fn()
      }));
    }
  } catch (error) {
    console.warn("Test setup warning:", error.message);
  }
});

// Clean up database between tests
beforeEach(async () => {
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Cleanup after all tests
afterAll(async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
  if (mongoServer) {
    await mongoServer.stop();
  }
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Helper functions for tests
global.createTestUser = (userId = "test_user_1", channelID = "test_channel") => ({
  userId,
  channelID,
});

global.createTestMessage = (text = "test message", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: { text },
});

global.createTestAttachment = (url = "https://example.com/image.png", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  message: {
    attachments: [
      {
        type: "image",
        payload: { url },
      },
    ],
  },
});

global.createTestPostback = (payload = "RECONNECT", userId = "test_user_1") => ({
  sender: { id: userId },
  recipient: { id: "test_channel" },
  postback: {
    title: payload.toLowerCase(),
    payload,
  },
});
