const request = require("supertest");
const app = require("../app.config");
const mongoose = require("mongoose");

// Mock the database operations to avoid actual database calls during testing
jest.mock("../scripts/DB/dataAccess");
jest.mock("../scripts/requestHandler");

describe("API Endpoints", () => {
  afterAll(async () => {
    // Clean up any open handles
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
    }
  });

  describe("GET /", () => {
    it("should return Hello World", async () => {
      const res = await request(app).get("/");
      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("Hello World!");
    });
  });

  describe("GET /verify", () => {
    it("should return service status", async () => {
      const res = await request(app).get("/verify");
      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("service is up and running!");
    });
  });

  describe("GET /webhook", () => {
    beforeEach(() => {
      process.env.VERIFY_TOKEN = "test_token";
    });

    it("should verify webhook with correct token", async () => {
      const res = await request(app)
        .get("/webhook")
        .query({
          "hub.verify_token": "test_token",
          "hub.challenge": "challenge_code"
        });
      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("challenge_code");
    });

    it("should return Hello There with incorrect token", async () => {
      const res = await request(app)
        .get("/webhook")
        .query({
          "hub.verify_token": "wrong_token",
          "hub.challenge": "challenge_code"
        });
      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("Hello There");
    });
  });

  describe("POST /webhook", () => {
    it("should handle valid page webhook", async () => {
      const webhookData = {
        object: "page",
        entry: [
          {
            id: "158625401348031",
            time: 1587575252627,
            messaging: [
              {
                sender: { id: "1510352682393194" },
                recipient: { id: "158625401348031" },
                timestamp: 1587575252468,
                message: {
                  mid: "m_hcv0C1G1kCNezoonvG4qY48XcpECsnIb-iLboQUg6Q8BMAKJyX9GeBR2KtABs0vsVlkbv0LUNeQ8JQHDOCf-QA",
                  text: "Hello test message",
                },
              },
            ],
          },
        ],
      };

      const res = await request(app)
        .post("/webhook")
        .send(webhookData);

      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("EVENT_RECEIVED");
    });

    it("should handle attachment message", async () => {
      const webhookData = {
        object: "page",
        entry: [
          {
            id: "158625401348031",
            time: 1587575252627,
            messaging: [
              {
                sender: { id: "1510352682393194" },
                recipient: { id: "158625401348031" },
                timestamp: 1587578848797,
                message: {
                  mid: "m_aZ8WySNVQq6lW8Xc6_L3MI8XcpECsnIb-iLboQUg6Q9OQiht8SciNGBUYLLY_7PsCwQCgdP1ebLGoM-xtPy5Xg",
                  attachments: [
                    {
                      type: "image",
                      payload: {
                        url: "https://example.com/image.png",
                        sticker_id: 369239263222822,
                      },
                    },
                  ],
                },
              },
            ],
          },
        ],
      };

      const res = await request(app)
        .post("/webhook")
        .send(webhookData);

      expect(res.statusCode).toEqual(200);
      expect(res.text).toEqual("EVENT_RECEIVED");
    });

    it("should return 404 for non-page webhook", async () => {
      const webhookData = {
        object: "user",
        entry: []
      };

      const res = await request(app)
        .post("/webhook")
        .send(webhookData);

      expect(res.statusCode).toEqual(404);
    });
  });
});
