const dataAccess = require("../scripts/DB/dataAccess");
const { replyBack } = require("../scripts/requestHandler");
const responseHandler = require("../scripts/responseHandler");
const UserController = require("../scripts/DB/schemes/user/user.ctrl");
const ChattingController = require("../scripts/DB/schemes/chatting/chatting.ctrl");
const WaitingController = require("../scripts/DB/schemes/waiting/waiting.ctrl");
const messages = require("../scripts/responseMessages");

// Mock the response handler to capture messages
jest.mock("../scripts/responseHandler");

describe("Integration Tests - Full Application Flow", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    responseHandler.handleMessage = jest.fn();
  });

  describe("User Matching Flow", () => {
    it("should match two users and start a chat session", async () => {
      const user1Data = createTestUser("user1", "channel1");
      const user2Data = createTestUser("user2", "channel2");

      // First user sends a message (should be added to waiting list)
      const message1 = createTestMessage("Hello from user1", "user1");
      message1.recipient.id = "channel1";
      
      await replyBack(message1);

      // Verify user1 gets a waiting message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );

      // Verify the message is one of the waiting messages
      const waitingCall = responseHandler.handleMessage.mock.calls.find(call => 
        messages.messages.includes(call[1].text)
      );
      expect(waitingCall).toBeDefined();

      jest.clearAllMocks();

      // Second user sends a message (should match with user1)
      const message2 = createTestMessage("Hello from user2", "user2");
      message2.recipient.id = "channel2";
      
      await replyBack(message2);

      // Verify both users get the "now connected" message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.nowConnected }
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user2" }),
        { text: messages.nowConnected }
      );

      // Verify user1 receives user2's message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: "Hello from user2" }
      );
    });

    it("should handle conversation between matched users", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));
      await ChattingController.create(user1, user2);

      jest.clearAllMocks();

      // User1 sends a message to user2
      const message1 = createTestMessage("How are you?", "user1");
      message1.recipient.id = "channel1";
      
      await replyBack(message1);

      // Verify user2 receives the message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user2" }),
        { text: "How are you?" }
      );

      jest.clearAllMocks();

      // User2 responds
      const message2 = createTestMessage("I'm good, thanks!", "user2");
      message2.recipient.id = "channel2";
      
      await replyBack(message2);

      // Verify user1 receives the response
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: "I'm good, thanks!" }
      );
    });
  });

  describe("Disconnection Flow", () => {
    it("should handle user disconnection from chat", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));
      await ChattingController.create(user1, user2);

      jest.clearAllMocks();

      // User1 disconnects
      const disconnectMessage = createTestPostback("DISCONNECT", "user1");
      disconnectMessage.recipient.id = "channel1";
      
      await replyBack(disconnectMessage);

      // Verify both users get the "partner left" message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.partnerLeft }
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user2" }),
        { text: messages.partnerLeft }
      );

      // Verify user1 gets the disconnect message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.disconnect }
      );

      // Verify chatting session is removed
      const chatting = await ChattingController.find(user1);
      expect(chatting).toBeNull();
    });

    it("should handle user reconnection", async () => {
      // Set up two matched users
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));
      await ChattingController.create(user1, user2);

      jest.clearAllMocks();

      // User1 reconnects (wants new partner)
      const reconnectMessage = createTestPostback("RECONNECT", "user1");
      reconnectMessage.recipient.id = "channel1";
      
      await replyBack(reconnectMessage);

      // Verify both users get the "partner left" message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.partnerLeft }
      );
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user2" }),
        { text: messages.partnerLeft }
      );

      // Verify user1 gets the reconnected message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.reconnected }
      );

      // Verify chatting session is removed
      const chatting = await ChattingController.find(user1);
      expect(chatting).toBeNull();
    });
  });

  describe("Edge Cases and Error Handling", () => {
    it("should handle same user messaging themselves", async () => {
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      await WaitingController.add(user1);

      jest.clearAllMocks();

      // User sends message while waiting (should get waiting message)
      const message = createTestMessage("Hello", "user1");
      message.recipient.id = "channel1";
      
      await replyBack(message);

      // Verify user gets a waiting message
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );

      // Verify it's one of the waiting messages
      const call = responseHandler.handleMessage.mock.calls[0];
      expect(messages.messages).toContain(call[1].text);
    });

    it("should handle help request", async () => {
      const helpMessage = createTestPostback("GET_STARTED", "user1");
      helpMessage.recipient.id = "channel1";
      
      await replyBack(helpMessage);

      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.help }
      );
    });

    it("should handle inactive user cleanup", async () => {
      // Create users with old timestamps
      const user1 = await UserController.create(createTestUser("user1", "channel1"));
      const user2 = await UserController.create(createTestUser("user2", "channel2"));
      
      // Make users inactive (old timestamp)
      user1.updatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      user2.updatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      await user1.save();
      await user2.save();
      
      await ChattingController.create(user1, user2);

      jest.clearAllMocks();

      // Try to send message (should trigger cleanup and add to waiting)
      const message = createTestMessage("Hello", "user1");
      message.recipient.id = "channel1";
      
      await replyBack(message);

      // Should get waiting message since inactive users were cleaned up
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        expect.objectContaining({ text: expect.any(String) })
      );
    });

    it("should handle multiple users in waiting queue", async () => {
      // Add first user to waiting
      const message1 = createTestMessage("Hello from user1", "user1");
      message1.recipient.id = "channel1";
      await replyBack(message1);

      jest.clearAllMocks();

      // Add second user - should match with first
      const message2 = createTestMessage("Hello from user2", "user2");
      message2.recipient.id = "channel2";
      await replyBack(message2);

      // Verify matching occurred
      expect(responseHandler.handleMessage).toHaveBeenCalledWith(
        expect.objectContaining({ userId: "user1" }),
        { text: messages.nowConnected }
      );

      jest.clearAllMocks();

      // Add third user - should go to waiting
      const message3 = createTestMessage("Hello from user3", "user3");
      message3.recipient.id = "channel3";
      await replyBack(message3);

      // Should get waiting message
      const waitingCall = responseHandler.handleMessage.mock.calls.find(call => 
        messages.messages.includes(call[1].text)
      );
      expect(waitingCall).toBeDefined();
    });
  });
});
