const { uploadImg } = require("../google.config");
const { google } = require("googleapis");

// Mock googleapis
jest.mock("googleapis");

describe("Google Drive Image Upload", () => {
  let mockDrive;
  let mockAuth;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDrive = {
      files: {
        create: jest.fn(),
        permissions: {
          create: jest.fn()
        }
      }
    };

    mockAuth = {
      authorize: jest.fn()
    };

    google.auth.JWT = jest.fn().mockImplementation(() => mockAuth);
    google.drive = jest.fn().mockReturnValue(mockDrive);
  });

  describe("uploadImg", () => {
    it("should upload image successfully", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      const mockPermissionResponse = {
        data: { id: "permission123" }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue(mockPermissionResponse);

      const result = await uploadImg("TestFolder", "base64imagedata", "test-image");

      expect(result).toBe("https://drive.google.com/file/d/file123/view");
      
      expect(google.auth.JWT).toHaveBeenCalledWith(
        expect.any(String), // client_email
        null,
        expect.any(String), // private_key
        [
          "https://www.googleapis.com/auth/drive",
          "https://www.googleapis.com/auth/drive.file",
          "https://www.googleapis.com/auth/drive.appdata"
        ]
      );

      expect(google.drive).toHaveBeenCalledWith({ version: "v3", auth: mockAuth });
      
      expect(mockDrive.files.create).toHaveBeenCalledWith({
        resource: {
          name: "test-image",
          mimeType: "image/png",
          parents: ["1CiAMppFd9gkJCWi96jno8bW9pmb6KTVH"]
        },
        media: {
          mimeType: "image/png",
          body: expect.any(Object) // stream
        }
      });

      expect(mockDrive.files.permissions.create).toHaveBeenCalledWith({
        fileId: "file123",
        resource: {
          role: "reader",
          type: "anyone"
        }
      });
    });

    it("should handle file creation error", async () => {
      const error = new Error("File creation failed");
      mockDrive.files.create.mockRejectedValue(error);

      await expect(uploadImg("TestFolder", "base64imagedata", "test-image"))
        .rejects.toThrow("File creation failed");

      expect(mockDrive.files.create).toHaveBeenCalled();
      expect(mockDrive.files.permissions.create).not.toHaveBeenCalled();
    });

    it("should handle permission creation error", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      const permissionError = new Error("Permission creation failed");
      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockRejectedValue(permissionError);

      await expect(uploadImg("TestFolder", "base64imagedata", "test-image"))
        .rejects.toThrow("Permission creation failed");

      expect(mockDrive.files.create).toHaveBeenCalled();
      expect(mockDrive.files.permissions.create).toHaveBeenCalled();
    });

    it("should handle authentication error", async () => {
      const authError = new Error("Authentication failed");
      google.auth.JWT.mockImplementation(() => {
        throw authError;
      });

      await expect(uploadImg("TestFolder", "base64imagedata", "test-image"))
        .rejects.toThrow("Authentication failed");
    });

    it("should sanitize filename by removing dots", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      await uploadImg("TestFolder", "base64imagedata", "test.image.with.dots");

      expect(mockDrive.files.create).toHaveBeenCalledWith({
        resource: {
          name: "testimagewithdots", // dots should be removed
          mimeType: "image/png",
          parents: ["1CiAMppFd9gkJCWi96jno8bW9pmb6KTVH"]
        },
        media: {
          mimeType: "image/png",
          body: expect.any(Object)
        }
      });
    });

    it("should handle empty base64 data", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      const result = await uploadImg("TestFolder", "", "empty-image");

      expect(result).toBe("https://drive.google.com/file/d/file123/view");
      expect(mockDrive.files.create).toHaveBeenCalled();
    });

    it("should handle null parameters", async () => {
      await expect(uploadImg(null, null, null))
        .rejects.toThrow();
    });

    it("should use correct parent folder ID", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      await uploadImg("TestFolder", "base64imagedata", "test-image");

      const createCall = mockDrive.files.create.mock.calls[0][0];
      expect(createCall.resource.parents).toEqual(["1CiAMppFd9gkJCWi96jno8bW9pmb6KTVH"]);
    });

    it("should set correct MIME type", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      await uploadImg("TestFolder", "base64imagedata", "test-image");

      const createCall = mockDrive.files.create.mock.calls[0][0];
      expect(createCall.resource.mimeType).toBe("image/png");
      expect(createCall.media.mimeType).toBe("image/png");
    });

    it("should create public read permission", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      await uploadImg("TestFolder", "base64imagedata", "test-image");

      expect(mockDrive.files.permissions.create).toHaveBeenCalledWith({
        fileId: "file123",
        resource: {
          role: "reader",
          type: "anyone"
        }
      });
    });
  });

  describe("Stream handling", () => {
    it("should create readable stream from base64 data", async () => {
      const mockFileResponse = {
        data: {
          id: "file123",
          webViewLink: "https://drive.google.com/file/d/file123/view"
        }
      };

      mockDrive.files.create.mockResolvedValue(mockFileResponse);
      mockDrive.files.permissions.create.mockResolvedValue({ data: { id: "permission123" } });

      await uploadImg("TestFolder", "base64imagedata", "test-image");

      const createCall = mockDrive.files.create.mock.calls[0][0];
      expect(createCall.media.body).toBeDefined();
      
      // The body should be a readable stream
      expect(typeof createCall.media.body.pipe).toBe("function");
    });
  });
});
