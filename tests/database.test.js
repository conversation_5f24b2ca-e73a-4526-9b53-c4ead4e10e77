const UserController = require("../scripts/DB/schemes/user/user.ctrl");
const ChattingController = require("../scripts/DB/schemes/chatting/chatting.ctrl");
const WaitingController = require("../scripts/DB/schemes/waiting/waiting.ctrl");
const User = require("../scripts/DB/schemes/user/user");
const Chatting = require("../scripts/DB/schemes/chatting/chatting");
const Waiting = require("../scripts/DB/schemes/waiting/waiting");

describe("Database Models and Controllers", () => {
  describe("User Model and Controller", () => {
    it("should create a new user", async () => {
      const userData = createTestUser("user123", "channel456");
      const user = await UserController.create(userData);
      
      expect(user).toBeDefined();
      expect(user.userId).toBe("user123");
      expect(user.channelID).toBe("channel456");
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
    });

    it("should find or create user", async () => {
      const userData = createTestUser("user123", "channel456");
      
      // First call should create the user
      const user1 = await UserController.find(userData);
      expect(user1).toBeDefined();
      expect(user1.userId).toBe("user123");
      
      // Second call should return the same user
      const user2 = await UserController.find(userData);
      expect(user2).toBeDefined();
      expect(user2._id.toString()).toBe(user1._id.toString());
    });

    it("should update user timestamp on find", async () => {
      const userData = createTestUser("user123", "channel456");
      
      const user1 = await UserController.find(userData);
      const firstUpdatedAt = user1.updatedAt;
      
      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const user2 = await UserController.find(userData);
      expect(user2.updatedAt.getTime()).toBeGreaterThan(firstUpdatedAt.getTime());
    });
  });

  describe("Waiting Model and Controller", () => {
    let testUser1, testUser2;

    beforeEach(async () => {
      testUser1 = await UserController.create(createTestUser("user1", "channel1"));
      testUser2 = await UserController.create(createTestUser("user2", "channel2"));
    });

    it("should add user to waiting list", async () => {
      const result = await WaitingController.add(testUser1);
      
      expect(result).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.user._id.toString()).toBe(testUser1._id.toString());
    });

    it("should fetch and match users from waiting list", async () => {
      // Add first user to waiting list
      await WaitingController.add(testUser1);
      
      // Fetch should return the first user and remove them from waiting
      const result = await WaitingController.fetch(testUser2);
      
      expect(result).toBeDefined();
      expect(result.user).toBeDefined();
      expect(result.user._id.toString()).toBe(testUser1._id.toString());
      
      // Verify user was removed from waiting list
      const waitingCount = await Waiting.countDocuments();
      expect(waitingCount).toBe(0);
    });

    it("should return null when no users in waiting list", async () => {
      const result = await WaitingController.fetch(testUser1);
      expect(result).toBeNull();
    });

    it("should remove user from waiting list", async () => {
      await WaitingController.add(testUser1);
      
      const result = await WaitingController.remove(testUser1);
      expect(result).toBeDefined();
      
      const waitingCount = await Waiting.countDocuments();
      expect(waitingCount).toBe(0);
    });

    it("should clean up old waiting entries", async () => {
      // Create an old waiting entry
      const oldWaiting = new Waiting({ user: testUser1 });
      oldWaiting.updatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      await oldWaiting.save();
      
      // Fetch should clean up old entries
      await WaitingController.fetch(testUser2);
      
      const waitingCount = await Waiting.countDocuments();
      expect(waitingCount).toBe(0);
    });
  });

  describe("Chatting Model and Controller", () => {
    let testUser1, testUser2;

    beforeEach(async () => {
      testUser1 = await UserController.create(createTestUser("user1", "channel1"));
      testUser2 = await UserController.create(createTestUser("user2", "channel2"));
    });

    it("should create a chatting session", async () => {
      const chatting = await ChattingController.create(testUser1, testUser2);
      
      expect(chatting).toBeDefined();
      expect(chatting.participants).toHaveLength(2);
      expect(chatting.participants[0].toString()).toBe(testUser1._id.toString());
      expect(chatting.participants[1].toString()).toBe(testUser2._id.toString());
    });

    it("should find chatting session by participant", async () => {
      await ChattingController.create(testUser1, testUser2);
      
      const chatting = await ChattingController.find(testUser1);
      
      expect(chatting).toBeDefined();
      expect(chatting.participants).toHaveLength(2);
      expect(chatting.participants.some(p => p._id.toString() === testUser1._id.toString())).toBe(true);
      expect(chatting.participants.some(p => p._id.toString() === testUser2._id.toString())).toBe(true);
    });

    it("should remove chatting session", async () => {
      await ChattingController.create(testUser1, testUser2);
      
      const removed = await ChattingController.remove(testUser1);
      
      expect(removed).toBeDefined();
      expect(removed.participants).toHaveLength(2);
      
      // Verify it was actually removed
      const chatting = await ChattingController.find(testUser1);
      expect(chatting).toBeNull();
    });

    it("should handle inactive user cleanup", async () => {
      // Create users with old timestamps
      testUser1.updatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      testUser2.updatedAt = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000); // 2 days ago
      await testUser1.save();
      await testUser2.save();
      
      await ChattingController.create(testUser1, testUser2);
      
      try {
        await ChattingController.find(testUser1);
        fail("Should have thrown OtherUserInactive error");
      } catch (error) {
        expect(error.message).toBe("OtherUserInactive");
      }
      
      // Verify chatting session was removed
      const chattingCount = await Chatting.countDocuments();
      expect(chattingCount).toBe(0);
    });
  });
});
