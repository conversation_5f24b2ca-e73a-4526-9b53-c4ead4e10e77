const messages = require("../scripts/responseMessages");

// Import the utility functions from app.socket.io.js
// Since they're not exported, we'll test them indirectly or create a separate utilities module

describe("Utility Functions and Messages", () => {
  describe("Response Messages", () => {
    it("should have waiting messages array", () => {
      expect(messages.messages).toBeDefined();
      expect(Array.isArray(messages.messages)).toBe(true);
      expect(messages.messages.length).toBeGreaterThan(0);
      
      messages.messages.forEach(message => {
        expect(typeof message).toBe("string");
        expect(message.length).toBeGreaterThan(0);
      });
    });

    it("should have connection messages", () => {
      expect(messages.nowConnected).toBeDefined();
      expect(typeof messages.nowConnected).toBe("string");
      expect(messages.nowConnected).toContain("match");
    });

    it("should have disconnection messages", () => {
      expect(messages.partnerLeft).toBeDefined();
      expect(typeof messages.partnerLeft).toBe("string");
      expect(messages.partnerLeft).toContain("left");

      expect(messages.disconnect).toBeDefined();
      expect(typeof messages.disconnect).toBe("string");
      expect(messages.disconnect).toContain("leaving");
    });

    it("should have reconnection message", () => {
      expect(messages.reconnected).toBeDefined();
      expect(typeof messages.reconnected).toBe("string");
      expect(messages.reconnected).toContain("partner");
    });

    it("should have help message", () => {
      expect(messages.help).toBeDefined();
      expect(typeof messages.help).toBe("string");
      expect(messages.help).toContain("message");
      expect(messages.help).toContain("connected");
    });
  });

  describe("Message Creation Functions", () => {
    // Since the functions are not exported from app.socket.io.js, 
    // we'll create a separate test for the logic

    describe("createMessage", () => {
      const createMessage = (msg) => {
        return msg && msg.text && { message: { text: msg.text } };
      };

      it("should create message with text", () => {
        const msg = { text: "Hello world" };
        const result = createMessage(msg);
        
        expect(result).toEqual({
          message: { text: "Hello world" }
        });
      });

      it("should return undefined for empty text", () => {
        const msg = { text: "" };
        const result = createMessage(msg);
        
        expect(result).toBeUndefined();
      });

      it("should return undefined for null message", () => {
        const result = createMessage(null);
        expect(result).toBeUndefined();
      });

      it("should return undefined for message without text", () => {
        const msg = { id: "123" };
        const result = createMessage(msg);
        
        expect(result).toBeUndefined();
      });
    });

    describe("createPostback", () => {
      const createPostback = (type) => {
        if (type) return { postback: { title: type, payload: type.toUpperCase() } };
      };

      it("should create postback with type", () => {
        const result = createPostback("reconnect");
        
        expect(result).toEqual({
          postback: {
            title: "reconnect",
            payload: "RECONNECT"
          }
        });
      });

      it("should handle uppercase conversion", () => {
        const result = createPostback("disconnect");
        
        expect(result).toEqual({
          postback: {
            title: "disconnect",
            payload: "DISCONNECT"
          }
        });
      });

      it("should return undefined for empty type", () => {
        const result = createPostback("");
        expect(result).toBeUndefined();
      });

      it("should return undefined for null type", () => {
        const result = createPostback(null);
        expect(result).toBeUndefined();
      });
    });

    describe("createAttachment", () => {
      const createAttachment = (imageUrl) => {
        if (imageUrl)
          return {
            message: {
              attachments: [
                {
                  type: "image",
                  payload: {
                    url: imageUrl,
                  },
                },
              ],
            },
          };
      };

      it("should create attachment with image URL", () => {
        const imageUrl = "https://example.com/image.png";
        const result = createAttachment(imageUrl);
        
        expect(result).toEqual({
          message: {
            attachments: [{
              type: "image",
              payload: {
                url: "https://example.com/image.png"
              }
            }]
          }
        });
      });

      it("should return undefined for empty URL", () => {
        const result = createAttachment("");
        expect(result).toBeUndefined();
      });

      it("should return undefined for null URL", () => {
        const result = createAttachment(null);
        expect(result).toBeUndefined();
      });
    });

    describe("createSender", () => {
      const createSender = (senderId) => {
        return {
          sender: {
            id: senderId,
          },
          recipient: {
            id: "app",
          },
        };
      };

      it("should create sender object", () => {
        const senderId = "user123";
        const result = createSender(senderId);
        
        expect(result).toEqual({
          sender: { id: "user123" },
          recipient: { id: "app" }
        });
      });

      it("should handle null sender ID", () => {
        const result = createSender(null);
        
        expect(result).toEqual({
          sender: { id: null },
          recipient: { id: "app" }
        });
      });
    });
  });

  describe("Data Access Helper Functions", () => {
    describe("getSender", () => {
      const getSender = (body) => {
        return {
          userId: body.sender.id,
          channelID: body.recipient.id,
        };
      };

      it("should extract sender information", () => {
        const body = {
          sender: { id: "user123" },
          recipient: { id: "channel456" }
        };
        
        const result = getSender(body);
        
        expect(result).toEqual({
          userId: "user123",
          channelID: "channel456"
        });
      });

      it("should handle missing sender", () => {
        const body = {
          recipient: { id: "channel456" }
        };
        
        expect(() => getSender(body)).toThrow();
      });

      it("should handle missing recipient", () => {
        const body = {
          sender: { id: "user123" }
        };
        
        expect(() => getSender(body)).toThrow();
      });
    });
  });

  describe("Random Message Selection", () => {
    it("should select random waiting message", () => {
      const getRandomWaitingMessage = () => {
        return messages.messages[
          Math.floor(Math.random() * messages.messages.length)
        ];
      };

      // Test multiple times to ensure it's working
      for (let i = 0; i < 10; i++) {
        const randomMessage = getRandomWaitingMessage();
        expect(messages.messages).toContain(randomMessage);
      }
    });

    it("should handle edge cases for random selection", () => {
      const getRandomFromArray = (arr) => {
        if (!arr || arr.length === 0) return null;
        return arr[Math.floor(Math.random() * arr.length)];
      };

      expect(getRandomFromArray([])).toBeNull();
      expect(getRandomFromArray(null)).toBeNull();
      expect(getRandomFromArray(undefined)).toBeNull();
      
      const singleItem = ["only item"];
      expect(getRandomFromArray(singleItem)).toBe("only item");
    });
  });
});
