{"name": "friendly-chat", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:unit": "node tests/runTests.js unit", "test:integration": "node tests/runTests.js integration", "test:api": "node tests/runTests.js api", "test:socket": "node tests/runTests.js socket", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "start": "node index.js", "nodemon": "nodemon index.js", "ngrok": "ngrok http 3000", "upload": "upload.bat", "clear_jest": "jest --clear<PERSON>ache"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/tests/setup.js"], "coveragePathIgnorePatterns": ["/node_modules/"], "testTimeout": 30000, "collectCoverageFrom": ["scripts/**/*.js", "app.*.js", "google.config.js", "!scripts/DB/schemes/**/index.js", "!**/node_modules/**"]}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"body-parser": "^1.19.0", "dotenv": "^8.2.0", "express": "^4.17.1", "googleapis": "^72.0.0", "mongoose": "^5.9.10", "prettier": "^2.0.5", "request": "^2.88.2", "socket.io": "^4.1.3"}, "devDependencies": {"babel-eslint": "^10.1.0", "jest": "^25.1.0", "mongodb-memory-server": "^10.1.4", "nock": "^13.1.1", "socket.io-client": "^4.8.1", "supertest": "^4.0.2"}}