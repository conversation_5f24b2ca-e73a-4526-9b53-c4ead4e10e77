{"name": "friendly-chat", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "start": "node index.js", "nodemon": "nodemon index.js", "ngrok": "ngrok http 3000", "upload": "upload.bat", "clear_jest": "jest --clear<PERSON>ache"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"]}, "author": "<EMAIL>", "license": "ISC", "dependencies": {"body-parser": "^1.19.0", "dotenv": "^8.2.0", "express": "^4.17.1", "googleapis": "^72.0.0", "mongoose": "^5.9.10", "prettier": "^2.0.5", "request": "^2.88.2", "socket.io": "^4.1.3"}, "devDependencies": {"babel-eslint": "^10.1.0", "jest": "^25.1.0", "nock": "^13.1.1", "supertest": "^4.0.2"}}