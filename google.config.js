const { google } = require("googleapis");
const stream = require("stream");
const credentials = require("./credentials.json");

const uploadImg = async (folderId, img, name) => {
  // process.env['NODE_TLS_REJECT_UNAUTHORIZED'] = 0

  const scopes = [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/drive.file",
    "https://www.googleapis.com/auth/drive.appdata",
  ];
  const auth = new google.auth.JWT(
    credentials.client_email,
    null,
    credentials.private_key,
    scopes
  );

  const drive = google.drive({ version: "v3", auth });

  const fileMetadata = {
    name: name.replace(/\./g, ""),
    mimeType: "image/png",
    parents: ["1CiAMppFd9gkJCWi96jno8bW9pmb6KTVH"],
    // webViewLink:,
  };

  // const uploadImg1 = img.split(/,(.+)/)[1];
  // //console.log(uploadImg1);
  const uploadImg = img;
  const buf = new Buffer.from(uploadImg, "base64"); // Added
  const bs = new stream.PassThrough(); // Added
  bs.end(buf); // Added

  const media = {
    body: bs,
  };

  let res = await drive.files.create({
    resource: fileMetadata,
    media: media,
    fields: "id",
  });
  // //console.log("the response is", res);
  let imageUrl = "https://drive.google.com/uc?export=view&id=" + res.data.id;
  //console.log("the data is ", imageUrl);
  return imageUrl;
};

module.exports.uploadImg = uploadImg;
